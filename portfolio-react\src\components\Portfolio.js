import React, { useEffect, useRef } from 'react';

const Portfolio = () => {
  const carouselTrackRef = useRef(null);

  useEffect(() => {
    const carouselTrack = carouselTrackRef.current;
    if (!carouselTrack) return;

    let isDragging = false;
    let isHovering = false;
    let startX;
    let startScrollLeft;
    let autoScrollInterval;
    let scrollSpeed = 1;

    // Auto-scroll function
    const startAutoScroll = () => {
      if (autoScrollInterval) clearInterval(autoScrollInterval);
      autoScrollInterval = setInterval(() => {
        if (!isDragging && !isHovering) {
          carouselTrack.scrollLeft += scrollSpeed;
          const trackWidth = carouselTrack.scrollWidth / 2;
          if (carouselTrack.scrollLeft >= trackWidth) {
            carouselTrack.scrollLeft = 0;
          }
        }
      }, 16);
    };

    const stopAutoScroll = () => {
      if (autoScrollInterval) {
        clearInterval(autoScrollInterval);
        autoScrollInterval = null;
      }
    };

    // Mouse events
    const handleMouseEnter = () => {
      isHovering = true;
    };

    const handleMouseLeave = () => {
      isHovering = false;
    };

    const handleMouseDown = (e) => {
      isDragging = true;
      isHovering = true;
      carouselTrack.classList.add('dragging');
      startX = e.pageX;
      startScrollLeft = carouselTrack.scrollLeft;
    };

    const handleMouseMove = (e) => {
      if (!isDragging) return;
      const x = e.pageX;
      const walk = (x - startX) * 1.8;
      carouselTrack.scrollLeft = startScrollLeft - walk;

      const trackWidth = carouselTrack.scrollWidth / 2;
      if (carouselTrack.scrollLeft >= trackWidth) {
        carouselTrack.scrollLeft = 0;
      } else if (carouselTrack.scrollLeft < 0) {
        carouselTrack.scrollLeft = trackWidth - 1;
      }
    };

    const handleMouseUp = () => {
      if (isDragging) {
        isDragging = false;
        carouselTrack.classList.remove('dragging');
        setTimeout(() => {
          if (!isHovering) {
            // Auto-scroll will resume
          }
        }, 100);
      }
    };

    const handleWheel = (e) => {
      e.preventDefault();
      const wheelDelta = e.deltaY;
      const scrollAmount = wheelDelta > 0 ? 50 : -50;
      carouselTrack.scrollLeft += scrollAmount;

      const trackWidth = carouselTrack.scrollWidth / 2;
      if (carouselTrack.scrollLeft >= trackWidth) {
        carouselTrack.scrollLeft = 0;
      } else if (carouselTrack.scrollLeft < 0) {
        carouselTrack.scrollLeft = trackWidth - 1;
      }
    };

    // Add event listeners
    carouselTrack.addEventListener('mouseenter', handleMouseEnter);
    carouselTrack.addEventListener('mouseleave', handleMouseLeave);
    carouselTrack.addEventListener('mousedown', handleMouseDown);
    carouselTrack.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    carouselTrack.addEventListener('wheel', handleWheel, { passive: false });

    // Start auto-scroll
    startAutoScroll();

    // Cleanup
    return () => {
      stopAutoScroll();
      carouselTrack.removeEventListener('mouseenter', handleMouseEnter);
      carouselTrack.removeEventListener('mouseleave', handleMouseLeave);
      carouselTrack.removeEventListener('mousedown', handleMouseDown);
      carouselTrack.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      carouselTrack.removeEventListener('wheel', handleWheel);
    };
  }, []);

  const portfolioItems = [
    {
      href: "https://threed-e-commerce.onrender.com",
      image: "/3D E-Comm.PNG",
      alt: "3D Ecommerce",
      title: "3D Ecommerce (Finish Soon)"
    },
    {
      href: "#",
      image: "/ex1.webp",
      alt: "Yalla Go Posters",
      title: "Will be deployed soon."
    },
    {
      href: "#",
      image: "/ex2.png",
      alt: "Nexit Brand Identity",
      title: "Will be deployed soon."
    },
    {
      href: "#",
      image: "/ex3.webp",
      alt: "Yalla Go Posters",
      title: "Will be deployed soon."
    },
    {
      href: "#",
      image: "/ex4.1.png",
      alt: "Yalla Go Posters",
      title: "Will be deployed soon."
    },
    {
      href: "#",
      image: "/ex5.png",
      alt: "Yalla Go Posters",
      title: "Will be deployed soon."
    },
    {
      href: "#",
      image: "/bussniss web UI.PNG",
      alt: "Business Web UI",
      title: "Available in git Will be deployed soon."
    }
  ];

  return (
    <section className="portfolio">
      <h2>Top Projects<br /></h2>
      <button className="discover-button" onClick={() => console.log('Discover more clicked')}>DISCOVER MORE</button>
      <div className="portfolio-carousel">
        <div className="carousel-track" ref={carouselTrackRef}>
          {/* Render items twice for infinite scroll */}
          {[...portfolioItems, ...portfolioItems].map((item, index) => (
            <div key={index} className="portfolio-item">
              <a href={item.href} target="_blank" rel="noopener noreferrer">
                <img src={item.image} alt={item.alt} />
                <p>{item.title}</p>
              </a>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Portfolio;
