<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Analytics Endpoints</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Analytics Endpoints Test</h1>
        
        <div class="test-section">
            <h3>Admin Login</h3>
            <input type="email" id="email" placeholder="Admin Email" value="<EMAIL>">
            <input type="password" id="password" placeholder="Admin Password" value="Adminboss">
            <button onclick="testLogin()">Login</button>
            <div id="login-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Test Experience Projects Analytics</h3>
            <button onclick="testExperienceAnalytics()">Test Experience Analytics</button>
            <div id="experience-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Test Portfolio Projects Analytics</h3>
            <button onclick="testPortfolioAnalytics()">Test Portfolio Analytics</button>
            <div id="portfolio-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Backend Status</h3>
            <button onclick="testPing()">Test Backend Ping</button>
            <div id="ping-result" class="result"></div>
        </div>
    </div>

    <script>
        const API_URL = 'https://porfolio-pro-backend.onrender.com';
        let adminToken = '';

        function displayResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                displayResult('login-result', 'Logging in...', 'info');
                
                const response = await fetch(`${API_URL}/api/admin/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                if (response.ok) {
                    const data = await response.json();
                    adminToken = data.token;
                    displayResult('login-result', `✅ Login successful! Token: ${adminToken.substring(0, 20)}...`, 'success');
                } else {
                    const errorText = await response.text();
                    displayResult('login-result', `❌ Login failed: ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                displayResult('login-result', `❌ Login error: ${error.message}`, 'error');
            }
        }

        async function testExperienceAnalytics() {
            if (!adminToken) {
                displayResult('experience-result', '⚠️ Please login first', 'error');
                return;
            }

            try {
                displayResult('experience-result', 'Testing experience analytics...', 'info');
                
                const response = await fetch(`${API_URL}/api/admin/experience-projects-analytics`, {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResult('experience-result', `✅ Experience analytics working!\nData: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    displayResult('experience-result', `❌ Experience analytics failed: ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                displayResult('experience-result', `❌ Experience analytics error: ${error.message}`, 'error');
            }
        }

        async function testPortfolioAnalytics() {
            if (!adminToken) {
                displayResult('portfolio-result', '⚠️ Please login first', 'error');
                return;
            }

            try {
                displayResult('portfolio-result', 'Testing portfolio analytics...', 'info');
                
                const response = await fetch(`${API_URL}/api/admin/portfolio-projects-analytics`, {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResult('portfolio-result', `✅ Portfolio analytics working!\nData: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    displayResult('portfolio-result', `❌ Portfolio analytics failed: ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                displayResult('portfolio-result', `❌ Portfolio analytics error: ${error.message}`, 'error');
            }
        }

        async function testPing() {
            try {
                displayResult('ping-result', 'Testing backend ping...', 'info');
                
                const response = await fetch(`${API_URL}/api/ping`);
                
                if (response.ok) {
                    const data = await response.json();
                    displayResult('ping-result', `✅ Backend is alive!\nResponse: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    displayResult('ping-result', `❌ Ping failed: ${response.status}`, 'error');
                }
            } catch (error) {
                displayResult('ping-result', `❌ Ping error: ${error.message}`, 'error');
            }
        }

        // Auto-test ping on page load
        window.onload = () => {
            testPing();
        };
    </script>
</body>
</html>
