export const jobsData = [
  {
    id: 1,
    slug: "frontend-receeto",
    title: "Frontend Developer Angular",
    company: "Company : Receeto",
    companyLink: "https://receeto.com",
    duration: "2/2025 - 6/2025",
    logo: "/Receeto_logo.jpg",
    logoAlt: "Receeto Logo",
    summary: "A smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.",
    roleOverview: "Note: Due to an active (NDA) contract, I'm unable to share detailed specifics about the project. However, I can briefly describe the technical scope and my personal contributions without disclosing confidential information.\n\nIt's a smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\n\nKey Highlights:\nExpense Tracking & Budgeting: Tools for monitoring transactions and setting personalized financial goals.\n\nSpending Analytics: Interactive category-based analysis to help users make informed decisions.\n\nPerformance Optimization:\n• Lazy loading for improved speed\n• Critical CSS and production build optimization\n\nTech Stack & Architecture:\n• Angular SPA with reactive state (signals)\n• Fully responsive design for mobile and desktop\n• Custom financial data visualizations using charts\n\nThis project showcases my expertise in Angular development, performance tuning, and crafting scalable, user-centric interfaces—while respecting the confidentiality of the client's program.",
    responsibilities: [
      "Develop responsive web applications using Angular and modern frontend technologies",
      "Implement financial data visualizations and interactive charts",
      "Optimize application performance through lazy loading and build optimization",
      "Create expense tracking and budgeting tools with real-time data processing",
      "Build responsive interfaces for both mobile and desktop platforms",
      "Implement Angular reactive state management using signals"
    ],
    skills: {
      "Frontend": ["Angular", "TypeScript", "RxJS", "Angular Signals", "Angular CLI"],
      "Styling": ["CSS3", "SASS/SCSS", "Angular Material", "Responsive Design", "Bootstrap"],
      "Tools & Testing": ["Git", "Angular CLI", "Webpack", "Lighthouse (for performance auditing)", "Figma"]
    },
    accomplishments: [
      {
        metric: "40%",
        description: "Improved application performance through lazy loading and build optimization"
      },
      {
        metric: "100%",
        description: "Responsive design compatibility across mobile and desktop platforms"
      },
      {
        metric: "NDA",
        description: "Confidential project delivered successfully while maintaining client privacy"
      }
    ],
    projects: [
      {
        title: "Img 1",
        description: "NDA - details confidential",
        images: ["../NDA.jpg", "../NDA.jpg", "../NDA.jpg"],
        technologies: ["Angular", "TypeScript", "Charts.js"],
        liveUrl: "https://receeto.com"
      },
      {
        title: "Img 2",
        description: "NDA - details confidential",
        images: ["../NDA.jpg", "../NDA.jpg"],
        technologies: ["Angular", "RxJS", "Angular Material"],
        liveUrl: "https://receeto.com"
      }
    ]
  },
  {
    id: 2,
    slug: "3d-ecommerce-platform",
    title: "3D-ecommerce platform UI/UX Designer",
    company: "DigitalStudio Creative",
    companyLink: "https://threed-e-commerce.onrender.com",
    duration: "2022 - 2023",
    logo: "/3D E Logo.png",
    logoAlt: "DigitalStudio Creative Logo",
    summary: "Developed an innovative 3D E-Commerce platform that revolutionizes online shopping through immersive 3D product visualization. Created interactive shopping experiences with photorealistic product models, increasing user engagement by 40%.",
    roleOverview: "As UI/UX Designer & Frontend Developer at DigitalStudio Creative, I spearheaded the development of a groundbreaking 3D E-Commerce platform that transforms how customers interact with products online. This project bridges the gap between online and in-store shopping experiences through advanced 3D visualization technologies.\n\nThe platform specializes in photorealistic product visualization for complex technology products including smartphones, computers, gaming consoles, and wearable technology. I designed and implemented a comprehensive solution that converts technical specifications into interactive 3D journeys, allowing users to explore every detail before purchasing.\n\nKey achievements include creating a mobile-first responsive design with touch-optimized 3D controls, implementing fallback mechanisms for graceful degradation, and establishing a scalable architecture that serves as the foundation for next-generation e-commerce experiences.",
    responsibilities: [
      "Design user interfaces and experiences for the 3D e-commerce platform",
      "Develop responsive frontend using React.js and modern web technologies",
      "Implement 3D model visualization with Three.js/WebGL and optimized mobile interactions",
      "Create comprehensive design systems for consistent user experience across devices",
      "Conduct usability testing to refine the shopping experience and 3D interactions",
      "Optimize application performance across various devices and network conditions",
      "Collaborate with clients to understand business requirements and technical constraints",
      "Develop fallback mechanisms and error boundaries for graceful failure handling",
      "Implement progressive enhancement and mobile-first responsive design principles"
    ],
    skills: {
      "Frontend": ["React.js", "Three.js/WebGL", "JavaScript", "CSS3", "React Router", "Axios"],
      "3D Technologies": ["Model Viewer", "GLTF/GLB", "OrbitControls", "Real-time Rendering"],
      "Backend": ["Node.js", "Express.js", "MongoDB", "RESTful APIs"],
      "Design & UX": ["Figma", "Responsive Design", "Mobile-First Design", "User Testing", "Performance Optimization"],
      "Tools & Deployment": ["Git", "Render", "Webpack", "Font Awesome", "Chrome DevTools"]
    },
    accomplishments: [
      {
        metric: "40%",
        description: "Increased user engagement through improved UX design and 3D interactions"
      },
      {
        metric: "95%",
        description: "Client satisfaction rate based on project feedback"
      }
    ],
    projects: [
      {
        title: "3D Product Visualization Engine",
        description: "Interactive 3D models with zoom, rotate, and pan capabilities for smartphones, computers, and gaming consoles. Features touch-optimized controls and fallback mechanisms.",
        images: [
          "../3D E Commerce Home.PNG",
          "../home mobil mode.PNG",
        ],
        technologies: ["Three.js", "WebGL", "GLTF/GLB", "OrbitControls"],
        liveUrl: "https://threed-e-commerce.onrender.com"
      },
      {
        title: "Mobile-Responsive Shopping Interface",
        description: "Comprehensive product grid with responsive layout, cart functionality, and mobile-optimized 3D controls. Minimum 44px touch targets for enhanced mobile experience.",
        images: [
          "../All product pc.PNG",
          "../all products mobil mode.PNG"
        ],
        technologies: ["React.js", "CSS3", "Responsive Design", "Mobile UX"],
        liveUrl: "https://threed-e-commerce.onrender.com"
      },
      {
        title: "Performance-Optimized Architecture",
        description: "Scalable client-server architecture with MongoDB backend, RESTful APIs, and automated deployment. Features fallback data and optimized build process.",
        images: [
          "../air pods pc mode.PNG",
          "../air pods mobil mode.PNG",
          
        ],
        technologies: ["Node.js", "Express.js", "MongoDB", "Render", "API Design"],
        liveUrl: "https://threed-e-commerce-backend.onrender.com"
      }
    ]
  },
  /*
  /* Job ID 3 and 4 are currently hidden
  {
    id: 3,
    slug: "junior-web-developer",
    title: "Junior Web Developer",
    company: "WebDev Agency",
    duration: "2021 - 2022",
    logo: "https://via.placeholder.com/120x120/00CED1/FFFFFF?text=WD",
    logoAlt: "WebDev Agency Logo",
    summary: "Developed custom WordPress themes and e-commerce solutions. Gained expertise in HTML, CSS, JavaScript, and PHP while working on diverse client projects ranging from small businesses to enterprise solutions.",
    roleOverview: "As a Junior Web Developer at WebDev Agency, I focused on building custom websites and e-commerce solutions for a diverse range of clients. This role provided me with a solid foundation in web development fundamentals and client communication skills.",
    responsibilities: [
      "Develop custom WordPress themes and plugins",
      "Build responsive websites using HTML, CSS, and JavaScript",
      "Create e-commerce solutions using WooCommerce and Shopify",
      "Collaborate with designers to implement pixel-perfect designs",
      "Optimize websites for performance and SEO",
      "Provide technical support and maintenance for client websites"
    ],
    skills: {
      "Frontend": ["HTML5", "CSS3", "JavaScript", "jQuery", "Bootstrap"],
      "Backend": ["PHP", "MySQL", "WordPress", "WooCommerce"],
      "Tools": ["Git", "Photoshop", "Chrome DevTools", "FTP", "cPanel"]
    },
    accomplishments: [
      {
        metric: "30+",
        description: "Websites successfully developed and launched"
      },
      {
        metric: "50%",
        description: "Improvement in page load speeds through optimization"
      },
      {
        metric: "100%",
        description: "Client satisfaction rate for delivered projects"
      }
    ],
    projects: [
      {
        title: "Restaurant Chain Website",
        description: "Built a multi-location restaurant website with online ordering system",
        images: [
          "https://via.placeholder.com/400x250/00CED1/FFFFFF?text=Restaurant+Website",
          "https://via.placeholder.com/400x250/008B8B/FFFFFF?text=Menu+System",
          "https://via.placeholder.com/400x250/20B2AA/FFFFFF?text=Order+Management"
        ],
        technologies: ["WordPress", "WooCommerce", "PHP"],
        liveUrl: "https://example-restaurant.com"
      },
      {
        title: "Real Estate Portal",
        description: "Developed property listing website with advanced search functionality",
        images: [
          "https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Real+Estate+Portal",
          "https://via.placeholder.com/400x250/228B22/FFFFFF?text=Property+Search",
          "https://via.placeholder.com/400x250/90EE90/FFFFFF?text=Listing+Details"
        ],
        technologies: ["HTML", "CSS", "JavaScript", "PHP"],
        liveUrl: "https://example-realestate.com"
      }
    ]
  },
  {
    id: 4,
    slug: "freelance-designer",
    title: "Freelance Designer",
    company: "Self-Employed",
    duration: "2020 - 2021",
    logo: "https://via.placeholder.com/120x120/32CD32/FFFFFF?text=FL",
    logoAlt: "Freelance Logo",
    summary: "Started my journey as a freelance graphic designer, creating logos, branding materials, and marketing collateral for local businesses. Built a strong foundation in design principles and client communication.",
    roleOverview: "Beginning my career as a freelance designer, I worked with local businesses to create compelling visual identities and marketing materials. This experience taught me the importance of understanding client needs and translating business objectives into effective design solutions.",
    responsibilities: [
      "Design logos and brand identities for small businesses",
      "Create marketing materials including flyers, brochures, and business cards",
      "Develop social media graphics and digital marketing assets",
      "Collaborate directly with business owners to understand their vision",
      "Manage multiple projects simultaneously while meeting deadlines",
      "Handle client communications and project billing"
    ],
    skills: {
      "Design Software": ["Adobe Illustrator", "Adobe Photoshop", "Adobe InDesign", "Canva"],
      "Design Skills": ["Logo Design", "Brand Identity", "Print Design", "Digital Graphics"],
      "Business Skills": ["Client Communication", "Project Management", "Time Management", "Pricing"]
    },
    accomplishments: [
      {
        metric: "20+",
        description: "Local businesses served with design solutions"
      },
      {
        metric: "4.9/5",
        description: "Average client rating on freelance platforms"
      },
      {
        metric: "90%",
        description: "Client retention rate for ongoing projects"
      }
    ],
    projects: [
      {
        title: "Local Coffee Shop Branding",
        description: "Complete brand identity including logo, menu design, and signage",
        images: [
          "https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Coffee+Shop+Brand",
          "https://via.placeholder.com/400x250/228B22/FFFFFF?text=Logo+Design",
          "https://via.placeholder.com/400x250/90EE90/FFFFFF?text=Menu+Design"
        ],
        technologies: ["Illustrator", "Photoshop", "InDesign"],
        liveUrl: "https://example-coffeeshop.com"
      },
      {
        title: "Fitness Studio Marketing Kit",
        description: "Comprehensive marketing materials for new fitness studio launch",
        images: [
          "https://via.placeholder.com/400x250/FF6347/FFFFFF?text=Fitness+Marketing",
          "https://via.placeholder.com/400x250/DC143C/FFFFFF?text=Brochure+Design",
          "https://via.placeholder.com/400x250/FF4500/FFFFFF?text=Social+Media+Kit"
        ],
        technologies: ["Photoshop", "Illustrator", "Print Design"],
        liveUrl: "https://example-fitness.com"
      }
    ]
  }
  */
];
