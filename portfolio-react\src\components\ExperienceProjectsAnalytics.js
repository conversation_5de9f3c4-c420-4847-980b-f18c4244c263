import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaArrowLeft, FaBriefcase, FaEye, FaClock, FaUsers, FaChartLine, FaExternalLinkAlt } from 'react-icons/fa';
import './ExperienceProjectsAnalytics.css';
import { preemptiveWakeup } from '../utils/backendWakeup';
import { formatDuration } from '../hooks/useVisitorTracking';

const ExperienceProjectsAnalytics = () => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    fetchAnalytics();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError('');

      // Wake up backend before attempting to fetch analytics
      console.log('Waking up backend...');
      await preemptiveWakeup();

      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/admin/login');
        return;
      }

      const API_URL = process.env.REACT_APP_API_URL;
      console.log('Fetching experience analytics from:', `${API_URL}/api/admin/experience-projects-analytics`);

      const response = await fetch(`${API_URL}/api/admin/experience-projects-analytics`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Response status:', response.status);

      if (response.status === 401) {
        localStorage.removeItem('token');
        navigate('/admin/login');
        return;
      }

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Response error:', errorText);
        throw new Error(`Failed to fetch analytics (${response.status}): ${errorText}`);
      }

      const data = await response.json();
      console.log('Analytics data received:', data);
      setAnalytics(data);
    } catch (err) {
      console.error('Analytics fetch error:', err);
      setError(`Error loading analytics: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleJobClick = (jobSlug) => {
    if (jobSlug && jobSlug !== 'unknown') {
      const url = `${window.location.origin}/job/${jobSlug}`;
      window.open(url, '_blank');
    }
  };

  const handleBackClick = () => {
    navigate('/admin/dashboard');
  };

  if (loading) {
    return (
      <div className="experience-analytics-container">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading Experience Analytics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="experience-analytics-container">
        <div className="error-message">
          <h3>Error Loading Analytics</h3>
          <p>{error}</p>
          <button onClick={fetchAnalytics} className="retry-button">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="experience-analytics-container">
      <div className="analytics-header">
        <button onClick={handleBackClick} className="back-button">
          <FaArrowLeft /> Back to Dashboard
        </button>
        <h1><FaBriefcase /> Experience Projects Analytics</h1>
        <p>Detailed analytics for professional experience section interactions</p>
      </div>

      {analytics && (
        <>
          {/* Summary Cards */}
          <div className="analytics-summary">
            <div className="summary-card">
              <FaBriefcase className="summary-icon" />
              <div className="summary-content">
                <h3>Total Projects</h3>
                <div className="summary-value">{analytics.totalProjects}</div>
              </div>
            </div>
            <div className="summary-card">
              <FaEye className="summary-icon" />
              <div className="summary-content">
                <h3>Total Views</h3>
                <div className="summary-value">{analytics.summary.totalViews}</div>
              </div>
            </div>
            <div className="summary-card">
              <FaClock className="summary-icon" />
              <div className="summary-content">
                <h3>Total Time</h3>
                <div className="summary-value">{formatDuration(analytics.summary.totalDuration)}</div>
              </div>
            </div>
            <div className="summary-card">
              <FaChartLine className="summary-icon" />
              <div className="summary-content">
                <h3>Avg Views/Project</h3>
                <div className="summary-value">{analytics.summary.avgViewsPerProject}</div>
              </div>
            </div>
          </div>

          {/* Projects List */}
          <div className="projects-analytics-list">
            <h2>Project Performance</h2>
            {analytics.data && analytics.data.length > 0 ? (
              <div className="projects-grid">
                {analytics.data.map((project, index) => (
                  <div key={index} className="project-analytics-card">
                    <div className="project-header">
                      <h3 className="project-title">{project.jobTitle}</h3>
                      <button 
                        className="view-project-btn"
                        onClick={() => handleJobClick(project.jobSlug)}
                        disabled={!project.jobSlug || project.jobSlug === 'unknown'}
                      >
                        <FaExternalLinkAlt /> View Project
                      </button>
                    </div>
                    
                    <div className="project-stats">
                      <div className="stat-item">
                        <FaEye className="stat-icon" />
                        <span className="stat-label">Views:</span>
                        <span className="stat-value">{project.totalViews}</span>
                      </div>
                      <div className="stat-item">
                        <FaClock className="stat-icon" />
                        <span className="stat-label">Duration:</span>
                        <span className="stat-value">{formatDuration(project.totalDuration)}</span>
                      </div>
                      <div className="stat-item">
                        <FaUsers className="stat-icon" />
                        <span className="stat-label">Unique Visitors:</span>
                        <span className="stat-value">{project.uniqueVisitorCount}</span>
                      </div>
                      <div className="stat-item">
                        <FaClock className="stat-icon" />
                        <span className="stat-label">Avg Duration:</span>
                        <span className="stat-value">{formatDuration(project.avgDuration)}</span>
                      </div>
                    </div>

                    <div className="project-slug">
                      <strong>Slug:</strong> {project.jobSlug || 'N/A'}
                    </div>

                    <div className="last-visit">
                      <strong>Last Visit:</strong> {
                        project.lastVisit ? 
                        new Date(project.lastVisit).toLocaleString() : 
                        'Never'
                      }
                    </div>

                    {project.recentInteractions && project.recentInteractions.length > 0 && (
                      <div className="recent-interactions">
                        <h4>Recent Interactions</h4>
                        <div className="interactions-list">
                          {project.recentInteractions.slice(0, 3).map((interaction, idx) => (
                            <div key={idx} className="interaction-item">
                              <span className="interaction-ip">{interaction.ip}</span>
                              <span className="interaction-time">
                                {new Date(interaction.timestamp).toLocaleString()}
                              </span>
                              <span className="interaction-duration">
                                {formatDuration(interaction.duration)}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-data">
                <p>No experience project analytics data available yet.</p>
                <p>Data will appear once users start interacting with the experience section.</p>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default ExperienceProjectsAnalytics;
